TEMPLATES_PLACEHOLDERS =
{
  'Payment' => [
    {'Payment Amount' => 'payment.amount'},
    {'Payment Date' => 'payment.payment_date'},
    {'Payment Method' => 'payment.payment_method'},
    {'Payment Type' => 'payment.payment_type'},
    {'Payment status' => 'payment.status'},
    {'Payment Summary' => 'payment.summary'},
    {'Payment Currency' => 'payment.currency'},
    {'Invoice Total' => 'payment.invoice.total'},
    {'Invoice Amount Due' => 'payment.invoice.amount_due'},
    {'Invoice Notes' => 'payment.invoice.notes'},
    {'Patient Full Name' => 'payment.patient.full_name'},
    {'Patient Pronouns' => 'payment.patient.pronouns'},
    {'Patient Email' => 'payment.patient.email'},
    {'Patient Phone' => 'payment.patient.phone'},
  ],
  'CalendarBooking' => [
    {'Start Date and Time' => 'calendar_booking.start_time'},
    {'Patient Full Name' => 'calendar_booking.patient.full_name'},
    {'Patient Pronouns' => 'calendar_booking.patient.pronouns'},
    {'Patient Email' => 'calendar_booking.patient.email'},
    {'Patient Phone' => 'calendar_booking.patient.phone'},
    {'Confirm Button' => 'calendar_booking.confirmation_button'},
    {'Decline Button' => 'calendar_booking.decline_button'},
    {'Confirm Link' => 'calendar_booking.confirmation_link'},
    {'Decline Link' => 'calendar_booking.decline_link'},
  ],
  'Invoice' => [
    {'Invoice Total' => 'invoice.total'},
    {'Invoice Amount Due' => 'invoice.amount_due'},
    {'Invoice Notes' => 'invoice.notes'},
    {'Patient Full Name' => 'invoice.patient.full_name'},
    {'Patient Pronouns' => 'invoice.patient.pronouns'},
    {'Patient Email' => 'invoice.patient.email'},
    {'Patient Phone' => 'invoice.patient.phone'},
  ],
  'Patient' => [
    {'Patient Full Name' => 'patient.full_name'},
    {'Patient Pronouns' => 'patient.pronouns'},
    {'Patient Email' => 'patient.email'},
    {'Patient Phone' => 'patient.phone'},
  ],
  'Treatment Plan' => [
    {'Treatment Plan Name' => 'treatment_plan.name'},
    {'Patient Full Name' => 'patient.full_name'},
    {'Patient Pronouns' => 'patient.pronouns'},
    {'Patient Email' => 'patient.email'},
    {'Patient Phone' => 'patient.phone'}
  ],
  'Recall' => [
    {'Patient Full Name' => 'recall.patient.full_name'},
    {'Treatment Patient Friendly Name' => 'recall.treatment.patient_friendly_name'},
  ]
}

DEFAULT_TEMPLATES = [
  {
    name: 'Send Medical History Link',
    subject: 'Your Medical History is ready for completion',
    title: 'Medical History is ready',
    section: 'Patient',
    text: <<~HTML
      <p>Hello {{patient.full_name}}!</p>

      <p>
        Here is your
        <a href="{{medical_history_url}}">Medical History link</a>
        ready for completion!
      </p>
    HTML
  },
  {
    name: 'Send Treatment Plan Link',
    subject: 'Please select your treatment plan',
    title: 'Your Treatment Plan is ready',
    section: 'TreatmentPlan',
    text: <<~HTML
      <p>Hello {{patient.full_name}}!</p>

      <p>
        Here is your
        <a href="{{treatment_plan.url}}">Treatment Plan link</a>
        ready for acceptance!
      </p>
    HTML
  },
  {
    name: 'Online Payment Link',
    subject: 'New Payment Request',
    title: 'New Payment Request',
    section: 'Patient',
    text: <<~HTML
      <p>Hello {{patient.full_name}}!</p>

      <p>
        A new payment request has been generated for you. Please click the button below to complete your payment.
        If you have any questions or concerns about this request, or if you believe this payment should not have been requested, please contact the clinic for assistance.
      </p>
    HTML
  },
  # TODO: ensure recalls section shows up in settings
  # TODO: add online booking link to recall reminder
  {
    name: 'Send Recall Reminder',
    subject: 'Time to book your dental recall visit',
    title: 'Time to book your dental recall visit',
    section: nil,
    text: <<~HTML
      <p>Hello {{recall.patient.full_name}}!</p>

      <p>Just a reminder you are now due your recall at {{recall.practice.name}}.</p>

      <p>Please contact the practice at {{recall.practice.phone}} or book online via our Patient Portal: {{new_patient_session_url}} at a convenient time.</p>

      <p>We look forward to seeing you!</p>
    HTML
  }
]
