# frozen_string_literal: true

module Admin
  class CalendarBookingsController < Admin::ApplicationController
    include Admin::CalendarBookingsHelper

    layout 'admin'

    before_action :set_filter_params, only: %i[
      staff_calendar modal edit_times_modal cancellation_list_modal cancel
      cancellation_list create slot_finder_offcanvas reschedule_offcanvas update
      drag_offcanvas offer_slot_to_patients staff_calendar_pdf
    ]
    before_action :set_calendar_booking, only: %i[
      modal edit_times_modal cancellation_list_modal cancel reschedule_offcanvas update
      update_status update_details update_payment_request move edit_cancellation_list_criteria
      update_cancellation_list_criteria remove_from_cancellation_list
    ]
    before_action :set_treatments
    before_action :set_calendar_reserved_block_types,
                  only: %i[staff_calendar modal edit_times_modal cancellation_list_modal staff_calendar_pdf]

    before_action { self.page_title = 'Calendar' }

    skip_before_action :verify_authenticity_token,
                       only: %i[cancellation_list update_status update_details move]

    def search
      @results = policy_scope(CalendarBooking)
                 .not_cancelled
                 .on_or_after_date(Date.current)
                 .search_by_query(params[:query])
                 .where(booking_type: %w[treatment recall])
                 .includes(:patient, :practitioner)

      session[:search_query] = params[:query]

      @results = @results.cancellation_list if params[:on_cancellation_list]

      respond_to do |format|
        format.js
      end
    end

    def modal
      @color = @calendar_reserved_block_types.where(treatment_id: @booking.treatment_id).first&.color
      @balance = @booking.patient&.balance
      @balance_status = @booking.patient&.balance_status

      respond_to do |format|
        format.js
        format.html do
          render partial: 'admin/calendar_bookings/staff_calendar/calendar_booking_popup', locals: { booking: @booking }
        end
      end
    end

    def edit_times_modal
      respond_to do |format|
        format.js
      end
    end

    def cancel
      authorize :calendar_booking, :update?

      if params[:all_future] == '1' && @booking.recurrent_identifier.present?
        policy_scope(CalendarBooking)
          .where(recurrent_identifier: @booking.recurrent_identifier)
          .where('start_time >= ?', @booking.start_time)
          .where.not(status: :cancelled)
          .find_each(&:cancel!)
      else
        @booking.cancel!
      end

      # CancellationListAutomationJob.perform_async(@booking.practitioner_id) # REBUILD

      flash[:notice] = 'Appointment cancelled successfully.'

      head :no_content
    rescue Pundit::NotAuthorizedError
      render json: { status: 'error', message: 'You are not authorized to cancel this booking.' },
             status: :forbidden
    end

    def cancellation_list_modal
      respond_to do |format|
        format.js
      end
    end

    def edit_cancellation_list_criteria
      render partial: 'admin/calendar_bookings/cancellation_list_criteria_modal', locals: { booking: @booking }
    end

    def update_cancellation_list_criteria
      authorize :calendar_booking, :update?

      if @booking.add_to_cancellation_list!(params[:cancellation_list_criteria])
        flash[:notice] = 'Cancellation list criteria updated successfully.'
      else
        flash[:alert] = 'Failed to update cancellation list criteria.'
      end
      redirect_to staff_cancellation_list_admin_calendar_bookings_path
    end

    def remove_from_cancellation_list
      authorize :calendar_booking, :update?

      if @booking.update(is_on_cancellation_list: false, cancellation_list_criteria: {})
        flash[:notice] = 'Booking removed from cancellation list.'
      else
        flash[:alert] = 'Failed to remove booking from cancellation list.'
      end
      redirect_to staff_cancellation_list_admin_calendar_bookings_path
    end

    def slot_finder_offcanvas
      @hidden_dentists = params[:hidden_dentists]

      if params[:charting_appointment_id]
        @charting_appointment = ChartingAppointment.find(params[:charting_appointment_id])
        @charting_appointment_patient = @charting_appointment.course_of_treatment&.patient
      elsif params[:patient_id]
        @charting_appointment_patient = Patient.find(params[:patient_id])
      end

      respond_to do |format|
        format.js
      end
    end

    def reschedule_offcanvas
      @patient = @booking.patient
      @practitioner = @booking.practitioner

      respond_to do |format|
        format.js
      end
    end

    def update
      authorize :calendar_booking, :update?

      @practice = Current.practice

      start_time = Time.zone.parse("#{calendar_booking_params[:date]} #{calendar_booking_params[:start_time]} London")
      end_time = Time.zone.parse("#{calendar_booking_params[:date]} #{calendar_booking_params[:end_time]} London")
      duration = (Time.zone.parse(calendar_booking_params[:end_time]) - Time.zone.parse(calendar_booking_params[:start_time])) / 60
      practitioner = User.clinicians.without_archived.find_by(id: calendar_booking_params[:practitioner_id])
      @date = calendar_booking_params[:date] if calendar_booking_params[:date].present?

      @booking.assign_attributes(start_time:, end_time:, practitioner:)

      _slots = Calendar::SlotFinder.new(
        practitioner: @booking.practitioner,
        start_date: calendar_booking_params[:date],
        end_date: calendar_booking_params[:date],
        start_time: calendar_booking_params[:start_time],
        end_time: calendar_booking_params[:end_time],
        duration: duration,
        practice: @practice
      ).run

      # Disable for the show...
      # unless slots.include?(start_time..end_time)
      #   flash[:alert] = 'Selected time slot is not available.'
      #   return redirect_to action: :staff_calendar, mode: @mode,
      #     date: @date, hidden_dentists: params[:hidden_dentists]
      # end

      if @booking.save
        # CancellationListAutomationJob.perform_async(@booking.practitioner_id) # REBUILD

        flash[:notice] = 'Appointment rescheduled successfully.'
      else
        flash[:alert] = "Failed to reschedule appointment. #{@booking.errors.full_messages.join(', ')}"
      end
      redirect_to action: :staff_calendar, mode: @mode, date: @date, hidden_dentists: params[:hidden_dentists], highlight_id: @booking.id
    end

    def staff_calendar
      @practitioners = User.clinicians.without_archived.includes([:image_attachment])

      if Current.practice_id
        @practice = Current.practice
        @practitioners = @practitioners.for_practice(@practice)
        @multisite_calendar = false
      else
        @practices = current_user.practices.includes([:image_attachment])
        @multisite_calendar = true
      end

      @holidays = policy_scope(Holiday)
                  .where(user_id: @practitioners.pluck(:id))
                  .approved
                  .for_dates(date_range.first, date_range.last)

      @calendar_day_notes = CalendarDayNote.where(date: date_range, user_id: @practitioners.pluck(:id))

      generator = Calendar::TimelineGenerator.new(@practice, (@date.wday if @mode == :day))

      @timeline = generator.run
      @practice_opening_time = generator.start_time
      @practice_closing_time = generator.end_time

      @shifts = Shift
                .clinical
                .where(user_id: @practitioners.pluck(:id), date: date_range)
                .where('event_start::time >= ?', @practice_opening_time&.strftime('%H:%M:%S'))

      @shifts = @shifts.where(practice_id: Current.practice_id) unless @multisite_calendar

      @practitioner_surgeries = @shifts.group_by(&:user_id).transform_values do |user_shifts|
        user_shifts.group_by(&:date).transform_values do |shifts_for_that_day|
          shifts_for_that_day.map(&:surgery_id).compact.uniq
        end
      end

      if @mode.in?(%i[day week]) && !@multisite_calendar
        @nurses_by_surgery_and_date = Calendar::NurseSurgeryFinder.new(date_range, @practice).run
      end

      case @mode
      when :day
        day_shifts = @shifts.select { |s| s.date == @date }
        @shifts_by_practitioner = day_shifts.group_by(&:user_id).transform_values do |user_shifts|
          user_shifts
            .map do |sh|
            { start: sh.event_start.in_time_zone.seconds_since_midnight, end: sh.event_end.in_time_zone.seconds_since_midnight,
              id: sh.id }
          end
            .sort_by { |h| h[:start] }
        end
      when :week
        @shifts_by_practitioner_and_day = @shifts.group_by(&:user_id).transform_values do |user_shifts|
          user_shifts.group_by(&:date).transform_values do |shifts|
            shifts
              .map do |sh|
              { start: sh.event_start.in_time_zone.seconds_since_midnight, end: sh.event_end.in_time_zone.seconds_since_midnight,
                id: sh.id }
            end
              .sort_by { |h| h[:start] }
          end
        end
      end

      @filtered_practitioners = @practitioners.where.not(id: params[:hidden_dentists] || [])

      @reserved_blocks = CalendarReservedBlock
                         .joins(:calendar_reserved_block_type)
                         .where(
                           practitioner_id: @filtered_practitioners.pluck(:id),
                           calendar_reserved_block_types: { practice_id: @practice&.id }
                         ).where('start_time::time >= ?', @practice_opening_time&.strftime('%H:%M:%S'))

      @bookings = policy_scope(CalendarBooking)
                  .where(practitioner_id: @filtered_practitioners.pluck(:id).uniq)
                  .where('start_time::time >= ?', @practice_opening_time&.strftime('%H:%M:%S'))
                  .not_cancelled
                  .between_dates(date_range.first, date_range.last)
                  .includes(*calendar_booking_includes(@mode, multisite_calendar: @multisite_calendar))

      @bookings = @bookings.where(practice_id: Current.practice_id) unless @multisite_calendar

      # NOTE: only display practitioners who have bookings, shifts or holidays
      @filtered_practitioners = @filtered_practitioners.where(id: @shifts.pluck(:user_id).uniq).or(
        @filtered_practitioners.where(id: @holidays.pluck(:user_id).uniq)
      ).or(
        @filtered_practitioners.where(id: @bookings.pluck(:practitioner_id).uniq)
      )

      # TODO: or reserved blocks?

      @availability_data = Calendar::AvailabilityCalculator.new(@bookings, @calendar_reserved_block_types).run

      @highlight_booking = policy_scope(CalendarBooking).find(params[:highlight_id]) if params[:highlight_id]
    end

    # NOTE: single practice day mode only
    def staff_calendar_pdf
      generator = Calendar::TimelineGenerator.new(Current.practice, (@date.wday if @mode == :day))

      @timeline = generator.run
      @practice_opening_time = generator.start_time
      @practice_closing_time = generator.end_time

      @practitioners = policy_scope(User)
                       .clinicians
                       .without_archived
                       .where.not(id: params[:hidden_dentists] || [])
                       .for_practice(Current.practice)
                       .includes([:image_attachment])

      @practitioners = @practitioners.where(id: params[:print_dentists].split(',').map(&:to_i)) if params[:print_dentists].present?

      @bookings = policy_scope(CalendarBooking)
                  .where(practitioner_id: @practitioners.pluck(:id).uniq)
                  .where('start_time::time >= ?', @practice_opening_time&.strftime('%H:%M:%S'))
                  .not_cancelled
                  .between_dates(date_range.first, date_range.last)
                  .includes(:patient, :practitioner, :treatment)

      @separate_pages = params[:separate_pages] == 'true'

      respond_to do |format|
        format.html { render layout: false }

        format.pdf do
          render pdf: "Day Schedule - #{@date.strftime('%d %b %Y')}", formats: [:html]
        end
      end
    end

    def staff_cancellation_list
      @bookings = policy_scope(CalendarBooking) # .on_or_after_date(Date.current)
                  .not_cancelled
                  .cancellation_list
                  .where(booking_type: %w[treatment recall])
                  .includes(:patient)
                  .order(start_time: :asc)
    end

    def create
      authorize :calendar_booking, :create?

      @practice = Current.practice

      practitioner = User.clinicians.without_archived.find_by!(id: calendar_booking_params[:practitioner_id])

      duration = (Time.zone.parse(calendar_booking_params[:end_time]) - Time.zone.parse(calendar_booking_params[:start_time])) / 60
      start_time = Time.zone.parse("#{calendar_booking_params[:date]} #{calendar_booking_params[:start_time]} London")
      end_time = Time.zone.parse("#{calendar_booking_params[:date]} #{calendar_booking_params[:end_time]} London")

      _slots = Calendar::SlotFinder.new(
        practitioner: practitioner,
        start_date: calendar_booking_params[:date],
        end_date: calendar_booking_params[:date],
        start_time: calendar_booking_params[:start_time],
        end_time: calendar_booking_params[:end_time],
        duration: duration,
        practice: @practice
      ).run

      # Disable for the show...
      # unless slots.include?(start_time..end_time)
      #   flash[:alert] = 'Selected time slot is not available.'
      #   return redirect_to action: :staff_calendar, mode: @mode, date: @date,
      #     hidden_dentists: params[:hidden_dentists]
      # end

      @booking = CalendarBooking.new(
        practice_id: Current.practice_id,
        patient_id: calendar_booking_params[:patient_id],
        practitioner_id: calendar_booking_params[:practitioner_id],
        treatment_id: calendar_booking_params[:treatment_id],
        start_time: start_time,
        end_time: end_time,
        notes: calendar_booking_params[:notes],
        booked_by_id: current_user.id,
        status: :scheduled,
        is_on_cancellation_list: false,
        booking_type: calendar_booking_params[:booking_type],
        event_color: calendar_booking_params[:event_color]
      )

      if @booking.save
        create_recurring_calendar_bookings(calendar_booking_params, @booking) if calendar_booking_params[:repeat_event] == '1'

        if calendar_booking_params[:charting_appointment_id]
          @charting_appointment = ChartingAppointment.find(calendar_booking_params[:charting_appointment_id])
          @charting_appointment.update!(calendar_booking: @booking)

          # if @charting_appointment.booking_appointment_type # REBUILD: undef method booking_appointment_type
          #   treatment = Treatment.find_by(patient_friendly_name: @charting_appointment.booking_appointment_type)

          #   @booking.update!(treatment:) if treatment
          # end
        elsif calendar_booking_params[:recall_id].present?
          recall = Recall.find(calendar_booking_params[:recall_id])

          recall.update(calendar_booking_id: @booking.id)

          @booking.update(
            booking_type: :recall,
            practitioner_id: calendar_booking_params[:practitioner_id],
            treatment: recall.treatment,
            notes: recall.notes
          )
        elsif calendar_booking_params[:course_of_treatment_id].present? && @booking.booking_type != 'event'
          # NOTE: Create a charting appointment if a treatment plan is selected (Add Appointment modal)
          course_of_treatment =
            if calendar_booking_params[:course_of_treatment_id] == 'new'
              CourseOfTreatment.create!(
                patient: @booking.patient,
                dentist: @booking.practitioner,
                practice: @booking.practitioner.practices.last || Practice.first,
                lead_clinician: @booking.practitioner,
                name: "Auto Course of Treatment for Appt. on #{start_time.strftime('%d %b %Y')}"
              )
            else
              CourseOfTreatment.find(calendar_booking_params[:course_of_treatment_id])
            end

          course_of_treatment.charting_appointments.create!(
            calendar_booking: @booking,
            dentist: @booking.practitioner
          )
        end

        # CancellationListAutomationJob.perform_async(@booking.practitioner_id) # REBUILD

        flash[:notice] = 'Appointment created successfully.'

        redirect_to action: :staff_calendar,
                    mode: @mode,
                    date: @booking.start_time.to_date,
                    hidden_dentists: params[:hidden_dentists],
                    highlight_id: @booking.id
      else
        flash[:alert] = "Failed to create appointment. #{@booking.errors.full_messages.join(', ')}"
        redirect_to action: :staff_calendar, mode: @mode, date: @date, hidden_dentists: params[:hidden_dentists]
      end
    end

    def slots
      @practice = Current.practice
      @practitioner = User.clinicians.without_archived.find_by!(id: params[:dentist_id])

      @slots = Calendar::SlotFinder.new(
        practitioner: @practitioner,
        start_date: params[:date],
        end_date: params[:date],
        start_time: params[:start_time],
        end_time: params[:end_time],
        duration: params[:duration].presence || 30,
        practice: @practice
      ).run

      # NOTE: handle time until booking # TODO: move to a service
      if params[:charting_appointment_id]
        charting_appointment = ChartingAppointment.find(params[:charting_appointment_id])
        bookable_from = Calendar::BookableFromDateFinder.new(charting_appointment).call

        @slots = @slots.select do |slot|
          slot.begin >= bookable_from.to_time
        end
      end

      @alternative_slots = @slots.reject(&:preferred)
      @preferred_slots = @slots.select(&:preferred)
      @date = begin
        Date.parse(params[:date])
      rescue StandardError
        Date.current.tomorrow
      end

      render layout: false
    end

    def cancellation_list
      @practitioner = User.clinicians.without_archived.find_by!(id: params[:practitioner_id])

      @bookings = @practitioner
                  .practitioner_bookings
                  .not_cancelled
                  .cancellation_list
                  .where('EXTRACT(EPOCH FROM (end_time - start_time)) / 60 <= ?', params[:duration] ? params[:duration].to_i : 60)
                  .order(start_time: :asc)

      if params[:patient_id].present?
        @patient = Patient.find_by!(id: params[:patient_id])
        @bookings = @bookings.where(patient: @patient)
      end

      # TODO: offer only bookings which are later than selected slot

      render layout: false
    end

    # NOTE: Add Appointment / Cancellation List offcanvas
    def drag_offcanvas
      @practitioner = User.clinicians.without_archived.find_by!(id: params[:practitioner_id])

      # NOTE: if dragged from bottom to top...
      if Time.zone.parse(params[:end_time]) < Time.zone.parse(params[:start_time])
        params[:start_time], params[:end_time] = params[:end_time], params[:start_time]
      end

      @booking = @practitioner.practitioner_bookings.new(
        start_time: Time.zone.parse("#{params[:date]} #{params[:start_time]} London"),
        end_time: Time.zone.parse("#{params[:date]} #{params[:end_time]} London")
      )

      @reserved_block = CalendarReservedBlock.find_by_times(
        Time.zone.parse("#{params[:date]} #{params[:start_time]} London"),
        Time.zone.parse("#{params[:date]} #{params[:end_time]} London"),
        @practitioner.id
      )

      @date_for_select = begin
        Date.parse(params[:date])
      rescue StandardError
        Date.current
      end

      @bookings = @practitioner
                  .practitioner_bookings
                  .not_cancelled
                  .cancellation_list
                  .on_or_after_date(Date.current)
                  .includes(:patient)
                  .order(start_time: :asc)

      respond_to do |format|
        format.js
      end
    end

    # NOTE: not used until we have proper patient notification.
    def offer_slot_to_patients
      authorize :calendar_booking, :offer_slot?

      start_time = Time.zone.parse("#{params[:date]} #{params[:start_time]} London")
      end_time = Time.zone.parse("#{params[:date]} #{params[:end_time]} London")

      Calendar::SlotOfferer.new(
        bookings: policy_scope(CalendarBooking).where(id: params[:booking_ids].compact),
        start_time:,
        end_time:
      ).run

      flash[:notice] = 'Slot offered successfully.'
      redirect_to action: :staff_calendar, mode: params[:mode], date: params[:date],
                  hidden_dentists: params[:hidden_dentists]
    end

    def update_status
      authorize :calendar_booking, :update?

      @booking.update!(status: calendar_booking_params[:status])

      flash[:notice] = 'Appointment status updated successfully.'

      render json: { status: 'success' }
    rescue Pundit::NotAuthorizedError
      render json: { status: 'error', message: 'You are not authorized to update this booking.' },
             status: :forbidden
    end

    def update_details
      authorize :calendar_booking, :update?

      date = @booking.start_time.to_date
      start_time = Time.zone.parse("#{date} #{calendar_booking_params[:start_time]} London")
      end_time = Time.zone.parse("#{date} #{calendar_booking_params[:end_time]} London")

      @date = calendar_booking_params[:date] if calendar_booking_params[:date].present?

      bookings_to_check = @booking.practitioner.practitioner_bookings.not_cancelled.between_dates(date,
                                                                                                  date).where.not(id: @booking.id)

      if bookings_to_check.none? { _1.overlaps?(start_time, end_time) }
        @booking.update(
          start_time: start_time,
          end_time: end_time,
          practitioner: @booking.practitioner,
          notes: calendar_booking_params[:notes],
          treatment_id: calendar_booking_params[:treatment_id]
        )

        flash[:notice] = 'Appointment time updated successfully.'
        render json: { status: 'success', message: 'Appointment times updated successfully.' }
      else
        render json: { status: 'error', message: 'Selected time slot is not available.' },
               status: :unprocessable_content
      end
    rescue Pundit::NotAuthorizedError
      render json: { status: 'forbidden', message: 'You are not authorized to update this booking.' },
             status: :forbidden
    end

    def update_payment_request
      authorize :calendar_booking, :update?

      @booking.update(
        payment_request_status: calendar_booking_params[:payment_request_status],
        payment_request_amount: calendar_booking_params[:payment_request_amount]
      )

      flash[:notice] = 'Payment request updated successfully.'
      render json: { status: 'success', message: 'Payment request updated successfully.' }
    rescue Pundit::NotAuthorizedError
      render json: { status: 'forbidden', message: 'You are not authorized to update this booking.' },
             status: :forbidden
    end

    def move
      authorize :calendar_booking, :update?

      start_time = Time.zone.parse("#{calendar_booking_params[:date]} #{calendar_booking_params[:start_time]} London")
      end_time = start_time + (@booking.end_time - @booking.start_time)

      @booking.update!(start_time:, end_time:)

      flash[:notice] = 'Appointment time updated successfully.'

      render json: { status: 'success' }
    rescue Pundit::NotAuthorizedError
      render json: { status: 'error', message: 'You are not authorized to update this booking.' },
             status: :forbidden
    end

    private

    def date_range
      case @mode
      when :day
        @date..@date
      when :week
        start_of_week = @date.beginning_of_week(:monday)
        start_of_week..(start_of_week + 6.days)
      when :month
        (@date.beginning_of_month - 7.days)..(@date.end_of_month + 7.days)
      end
    end

    def calendar_booking_params
      params.require(:calendar_booking).permit(
        :patient_id, :practitioner_id, :treatment_id, :start_time, :end_time, :notes, :date, :booking_type, :recall_id,
        :charting_appointment_id, :course_of_treatment_id, :status, :delay,
        :payment_request_status, :payment_request_amount,
        # NOTE: event booking_type fields
        :event_color,
        :repeat_event,
        :repeat_pattern,
        :end_repeat_date,
        :repeat_indefinitely,
        cancellation_list_criteria: {},
        repeat_days: []
      )
    end

    def set_calendar_booking
      @booking = policy_scope(CalendarBooking).find(params[:id])
    end

    def set_treatments
      @treatments = policy_scope(Treatment).all.joins(:calendar_reserved_block_types)
    end

    def set_calendar_reserved_block_types
      @calendar_reserved_block_types = policy_scope(CalendarReservedBlockType).all
    end

    def set_filter_params
      @mode =
        if params[:mode].in?(%w[day week month])
          params[:mode].to_sym
        else
          :day
        end

      @date = params[:date] ? Date.parse(params[:date]) : Time.zone.today

      # TODO: refactor, a temporary fix for AJAX requests
      return unless params[:hidden_dentists].is_a?(String)

      params[:hidden_dentists] = begin
        JSON.parse(params[:hidden_dentists])
      rescue StandardError
        []
      end

      return unless params[:hidden_dentists].is_a?(String)

      params[:hidden_dentists] =
        begin
          JSON.parse(params[:hidden_dentists])
        rescue StandardError
          []
        end
    end

    # TODO: move to a service
    # TODO: get rid of recurrence fields on CalendarBooking, it's not needed
    def create_recurring_calendar_bookings(attrs, reference_booking)
      start_date = reference_booking.start_time
      recurrent_identifier = SecureRandom.uuid

      reference_booking.update!(recurrent_identifier: recurrent_identifier)

      if attrs[:repeat_indefinitely] == '1'
        end_date = start_date + 1.year
      elsif attrs[:end_repeat_date].present?
        end_repeat_date = Date.parse(attrs[:end_repeat_date])
        end_date = end_repeat_date.end_of_day
      else
        end_date = (start_date + 3.months).end_of_day
      end

      repeat_days = (attrs[:repeat_days] || []).map(&:to_i)
      pattern = attrs[:repeat_pattern]

      current_date = start_date.to_date + 1.day
      week_counter = 0

      while current_date.end_of_day <= end_date
        if repeat_days.include?(current_date.wday) && (pattern == 'weekly' || (pattern == 'alternating' && week_counter.even?))
          new_start_time = Time.zone.parse("#{current_date} #{reference_booking.start_time.strftime('%H:%M:%S')} London")
          new_end_time   = Time.zone.parse("#{current_date} #{reference_booking.end_time.strftime('%H:%M:%S')} London")

          CalendarBooking.create!(
            practice_id: reference_booking.practice_id,
            practitioner_id: reference_booking.practitioner_id,
            treatment_id: reference_booking.treatment_id,
            start_time: new_start_time,
            end_time: new_end_time,
            notes: reference_booking.notes,
            booked_by_id: reference_booking.booked_by_id,
            status: :scheduled,
            event_color: reference_booking.event_color,
            booking_type: 'event',
            is_on_cancellation_list: false,
            recurrent_identifier: recurrent_identifier
          )
        end

        current_date += 1.day

        week_counter += 1 if current_date.monday?
      end
    end

    def calendar_booking_includes(mode, multisite_calendar: false)
      if mode.in?(%i[day week]) && !multisite_calendar
        [
          :treatment,
          { patient: [
              :medical_histories,
              :actions,
              :payments,
              :lab_works,
              :payment_plans,
              { invoices: [:invoice_items, { payments: :refunds }] }
            ],
            charting_appointments: %i[
              lab_works
              signature_requests
            ] }
        ]
      else
        %i[
          patient
          treatment
        ]
      end
    end
  end
end
