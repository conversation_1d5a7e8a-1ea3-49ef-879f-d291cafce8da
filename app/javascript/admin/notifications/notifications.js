// Import the global sidebar manager
import SidebarManager from '../shared/sidebar_manager';

// Notification handling
class NotificationManager {
  constructor() {
    this.toastContainer = document.getElementById('notification-toast-container');
    this.notificationSidebar = document.getElementById('notification-sidebar');
    this.overlay = document.getElementById('shared-sidebar-overlay');
    this.closeNotificationsBtn = document.getElementById('close-notifications');
    this.remindInDropdown = document.getElementById('remind-in-dropdown-container');
    this.currentNotificationId = null;
    this.currentNotificationIndex = null;

    this.initializeEventListeners();
    this.initializeTabs();
    this.initializePusher();

    // Register with the global sidebar manager
    if (this.notificationSidebar) {
      SidebarManager.register('notification-sidebar',
        () => this.openSidebarInternal(),
        () => this.closeSidebarInternal()
      );
    }
  }

  initializeEventListeners() {
    // Sidebar toggle
    this.closeNotificationsBtn?.addEventListener('click', () => this.closeSidebar());

    // Notification toggle is handled by notification_toggle.js to avoid conflicts

    // Set reminder button
    const setReminderBtn = document.getElementById('set-reminder');
    setReminderBtn?.addEventListener('click', () => this.handleSetReminder());

    // Handle action buttons (including remind-in buttons)
    document.addEventListener('click', (e) => {
      // const actionButton = e.target.closest('.action-button-split');
      const actionButton = e.target.closest('.notification-dropdown');
      if (!actionButton) return;

      const notificationId = e.target.dataset.id;
      const action = e.target.dataset.action;
      const index = e.target.dataset.index;

      if (action === 'remind_in') {
        this.showRemindInSweetAlert(notificationId, index);
      } else {
        this.handleAction(notificationId, action, index);
      }
    });

    const notificationContent = document.querySelector(".notification-content");
    if (notificationContent) {
      notificationContent.addEventListener("pusher:after-prepend", (e) => {
        $(".notification-sidebar .empty-state").remove();

        Array.from(e.target.querySelectorAll('.action-button-split:not(.remind-in-toggle)')).forEach(button => {
          button.addEventListener('click', (e) => {
            const notificationId = e.target.dataset.notificationId;
            const action = e.target.dataset.action;
            const index = e.target.dataset.index;
            this.handleAction(notificationId, action, index);
          });
        });
      });
    }

    const clearAllBtn = document.querySelector(".clear-all-button");
    if (clearAllBtn) {
      clearAllBtn.addEventListener("click", () => {
        this.clearAll();
      });
    }

    document.addEventListener("click", (e) => {
      const btn = e.target.closest(".notification-options-btn");
      if (btn) {
        const notificationId = btn.dataset.notificationId;
        const dropdown = document.querySelector(`.notification-dropdown[data-dropdown-for="${notificationId}"]`);
        dropdown.classList.toggle("hidden");
        return;
      }

      document.querySelectorAll(".notification-dropdown").forEach(d => d.classList.add("hidden"));
    });

    document.addEventListener("click", (e) => {
      const item = e.target.closest(".dropdown-item");
      if (!item) return;

      const notificationId = item.dataset.id;
      const action = item.dataset.action;
      const index = item.dataset.index;

      this.handleAction(notificationId, action, index);
    });

    document.addEventListener("click", (event) => {
      const button = event.target.closest('[data-role="notification-close"]');
      if (!button) return;

      const notificationId = button.getAttribute("data-notification-id");
      const action = button.getAttribute("data-action");
      const index = parseInt(button.getAttribute("data-index"), 10);

      if (!notificationId || !action) return;

      this.handleAction(notificationId, action, index);
    });
  }

  initializeTabs() {
    const tabs = document.querySelectorAll('#notification-sidebar .tab-button');

    tabs.forEach(tab => {
      const isActive = tab.classList.contains('active');
      const color = tab.dataset.color;
      const span = tab.querySelector('.tab-text');

      if (isActive) {
        tab.classList.add('flex-1', 'px-5');
        tab.classList.remove('bg-white', 'text-gray-600', 'px-3');

        if (color === 'blue') {
          tab.classList.add('text-blue-800');
          tab.style.background = 'linear-gradient(to right, rgb(191, 219, 254), rgb(147, 197, 253))';
        } else if (color === 'purple') {
          tab.classList.add('text-purple-800');
          tab.style.background = 'linear-gradient(to right, rgb(221, 214, 254), rgb(196, 181, 253))';
        }

        if (span) {
          span.classList.add('opacity-100', 'w-auto', 'ml-1.5');
          span.classList.remove('opacity-0', 'w-0');
        }
      } else {
        tab.classList.remove('active', 'text-blue-800', 'text-purple-800', 'flex-1', 'px-5');
        tab.classList.add('bg-white', 'text-gray-600', 'px-3', 'tab');
        tab.style.background = '';

        if (span) {
          span.classList.remove('opacity-100', 'w-auto', 'ml-1.5');
          span.classList.add('opacity-0', 'w-0');
        }
      }

      tab.addEventListener('click', () => {
        tabs.forEach(t => {
          t.classList.remove('active', 'text-blue-800', 'text-purple-800', 'flex-1', 'px-5');
          t.classList.add('bg-white', 'text-gray-600', 'px-3', 'tab');
          t.style.background = '';

          const s = t.querySelector('.tab-text');
          if (s) {
            s.classList.remove('opacity-100', 'w-auto', 'ml-1.5');
            s.classList.add('opacity-0', 'w-0');
          }
        });

        tab.classList.add('active', 'flex-1', 'px-5');
        tab.classList.remove('bg-white', 'text-gray-600', 'px-3');

        if (color === 'blue') {
          tab.classList.add('text-blue-800');
          tab.style.background = 'linear-gradient(to right, rgb(191, 219, 254), rgb(147, 197, 253))';
        } else if (color === 'purple') {
          tab.classList.add('text-purple-800');
          tab.style.background = 'linear-gradient(to right, rgb(221, 214, 254), rgb(196, 181, 253))';
        }

        if (span) {
          span.classList.remove('opacity-0', 'w-0');
          span.classList.add('opacity-100', 'w-auto', 'ml-1.5');
        }

        this.filterNotifications();
      });
    });
  }

  initializePusher() {
    const [pusher, channel] = connectPusherActions(`private-notifications.${window._currentUserId}`);

    channel.bind("notification", data => {
      // Log the notification data to help with debugging
      console.log("Received notification via Pusher:", data);

      // Make sure we have all the required data
      if (data && data.id) {
        // Handle the notification (show toast)
        this.handleNotification(data);

        var snd = new Audio("/audio/beep-2.mp3");
        snd.play();
        // Also refresh the notifications list in the sidebar
        // this.refreshNotifications();

        // Update the notification count badge
        this.updateNotificationCount();
      } else {
        console.error("Received invalid notification data:", data);
      }
    });

    channel.bind("attended", () => {
      // this.patientAttended();
    });
  }

  refreshNotifications() {
    // Reload the notifications in the sidebar
    $.ajax({
      url: '/admin/notifications',
      type: 'GET',
      dataType: 'html',
      headers: {
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      success: (html) => {
        // Update the notification content
        const notificationContent = document.querySelector('.notification-content');
        if (notificationContent) {
          notificationContent.innerHTML = html;
        }

        // Update the notification count
        const count = document.querySelectorAll('.notification-card').length;
        const badge = document.querySelector('#notification-toggle .badge');
        if (badge) {
          badge.textContent = count;
          badge.style.display = count > 0 ? 'flex' : 'none';
        }
      }
    });
  }

  updateNotificationCount() {
    // Get the current count of unread notifications
    const count = document.querySelectorAll('.notification-block').length;
    const badge = document.querySelector('#notification_count');
    if (badge) {
      badge.textContent = count;
      badge.style.display = count > 0 ? 'flex' : 'none';
    }
  }

  handleNotification(data) {
    console.log("Handling notification:", data);

    // Ensure we have valid data before showing the toast
    if (!data || !data.id) {
      console.error("Invalid notification data:", data);
      return;
    }

    // Transform actions for the toast
    const transformedActions = (data.actions || []).map((action, index) =>
      this.transformAction(data, action, index)
    );

    const dropdown = document.querySelector(`.notification-dropdown[data-dropdown-for="${data.id}"]`);
    if (dropdown) {
      dropdown.innerHTML = transformedActions
          .map((action, index) => {
            return `
          <button class="dropdown-item block w-full text-left px-4 py-2 text-sm text-[#4F46E5] hover:bg-[#F0F0FF]" data-action="${action.action}" data-index="${index}" data-id="${data.id}">
            ${action.text}
          </button>
        `;
          })
          .join("");
    }

    // Show the toast notification
    this.showToast({ ...data, actions: transformedActions });
  }

  transformAction(data, action, index) {
    return {
      ...action,
      callback: () => {
        this.handleAction(data.id, action.action, index);
      }
    }
  }

  handleAction(notificationId, action, index) {
    fetch(`/admin/notifications/${notificationId}/handle_action`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({ action_name: action, index })
    }).then(response => {
      if (response.ok) {
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.indexOf("application/json") !== -1) {
          response.json().then(json => {
            if (json.redirect) window.location.href = json.redirect;
            if (json.action_type === 'remind_in') {
              this.showRemindInModal(json.action_id);
            }
          });
        }

        // Remove the notification card after successful action
        // const card = document.querySelector(`[data-notification-id="${notificationId}"]`);
        // card?.remove();
        // this.checkVisibleNotifications();
        // this.updateNotificationCount();

        const card = document.querySelector(`[data-notification-id="${notificationId}"]`);
        if (!card) return;

        const group = card.closest('.notification-group');
        const type = group?.dataset.notificationType;
        const expanded = group?.querySelector(`.notification-group-expanded[data-notification-type="${type}"]`);
        const stackedBlock = group?.querySelector(`.notification-stacked-block[data-notification-type="${type}"]`);

        const isFirst = card.parentElement === group;
        card.remove();

        if (isFirst && expanded) {
          const nextCard = expanded.querySelector('.relative[data-notification-id]');
          if (nextCard) {
            expanded.removeChild(nextCard);
            group.insertBefore(nextCard, stackedBlock);

            const stackedWrapper = nextCard.querySelector('.absolute.inset-0.z-0');
            if (stackedWrapper) {
              stackedWrapper.classList.toggle('hidden', !expanded.classList.contains('hidden'));
            }
          }

          const visibleChildrenCount = Array.from(expanded.children).filter(child => !child.classList.contains('hidden')).length;
          if (visibleChildrenCount === 0) {
            const stackedWrapper = nextCard.querySelector('.absolute.inset-0.z-0');
            stackedWrapper.classList.add('hidden');
            stackedBlock?.remove();
            expanded?.remove();
          } else {
            this.updateStackedCount(stackedBlock, visibleChildrenCount, expanded);
          }
        } else if (!isFirst && expanded && stackedBlock) {
          const visibleChildrenCount = Array.from(expanded.children).filter(child => !child.classList.contains('hidden')).length;
          this.updateStackedCount(stackedBlock, visibleChildrenCount, expanded);
        }

        this.checkVisibleNotifications();
        this.updateNotificationCount();
      }
    });
  }

  updateStackedCount(block, count, expanded) {
    const span = block.querySelector('span');
    const type = block.dataset.notificationType;

    if (count <= 0) {
      block.remove();
      return;
    }

    if (span) {
      const humanized = this.humanizeString(type);
      const isHidden = expanded?.classList.contains('hidden');

      span.textContent = isHidden
          ? `${count} more ${humanized} notifications`
          : `Hide ${count} ${humanized} notifications`;
    }
  }

  humanizeString(str) {
    if (!str) return '';
    return str.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  handleRemindIn(notificationId, index, minutes) {
    $.ajax({
      url: `/admin/notifications/${notificationId}/handle_action`,
      type: 'POST',
      headers: {
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      data: {
        action_name: 'remind_in',
        index: index,
        minutes: minutes
      },
      success: (response) => {
        // Remove the notification from the sidebar
        const notificationCard = document.querySelector(`.notification-card[data-notification-id="${notificationId}"]`);
        if (notificationCard) {
          notificationCard.remove();
        }

        // Show success message
        if (response.message) {
          Swal.fire({
            title: 'Reminder Set',
            text: response.message,
            icon: 'success',
            timer: 3000,
            showConfirmButton: false
          });
        }
      },
      error: (error) => {
        console.error('Error setting reminder:', error);
        Swal.fire({
          title: 'Error',
          text: 'Failed to set reminder. Please try again.',
          icon: 'error'
        });
      }
    });
  }

  showRemindInModal(actionId) {
    const modal = document.getElementById('remindInModal');
    const remindActionIdInput = document.getElementById('remind-action-id');
    remindActionIdInput.value = actionId;

    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
  }

  showRemindInSweetAlert(notificationId, index) {
    Swal.fire({
      title: 'Remind in',
      html: `
        <div class="form-group">
          <select id="remind-time-select" class="form-select" style="border: 1px solid black; border-radius: 5px; padding: 5px; width: 100%;">
            <option value="">Select a time</option>
            <option value="5">5 minutes</option>
            <option value="15">15 minutes</option>
            <option value="30">30 minutes</option>
            <option value="60">1 hour</option>
            <option value="120">2 hours</option>
            <option value="240">4 hours</option>
            <option value="480">8 hours</option>
            <option value="1440">1 day</option>
          </select>
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: 'Set Reminder',
      cancelButtonText: 'Cancel',
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn btn-secondary',
        popup: 'remind-in-popup'
      },
      buttonsStyling: false,
      preConfirm: () => {
        const selectValue = document.getElementById('remind-time-select').value;
        if (!selectValue) {
          Swal.showValidationMessage('Please select a time');
          return false;
        }
        return selectValue;
      }
    }).then((result) => {
      if (result.isConfirmed && result.value) {
        const minutes = result.value;
        this.handleRemindIn(notificationId, index, minutes);
      }
    });
  }

  openSidebar() {
    // Use the global sidebar manager to handle opening
    SidebarManager.open('notification-sidebar');
  }

  // Internal function that actually performs the opening
  openSidebarInternal() {
    if (!this.notificationSidebar) {
      console.warn('Notification sidebar element not found');
      return;
    }
    // Use Tailwind transform classes for smooth animation
    this.notificationSidebar.classList.remove('translate-x-full');
    this.filterNotifications();
  }

  filterNotifications() {
    const activeTab = document.querySelector('#notification-sidebar .tab-button.active');
    const tab = activeTab?.dataset.tab;

    $(".notification-block").removeClass('hidden');

    if (tab === "today") {
      const date = new Date().toISOString().split("T")[0];
      $(`.notification-block:not([data-date="${date}"])`).addClass('hidden');
    }

    this.updateStackedGroups();
    this.checkVisibleNotifications();
  }

  updateStackedGroups() {
    const groups = document.querySelectorAll(".notification-group");

    groups.forEach(group => {
      const type = group.dataset.notificationType;
      const stackedItems = group.querySelector(".notification-group-expanded");
      const span = group.querySelector(".notification-stacked-block span");

      if (!stackedItems || !span) return;

      const visibleItems = Array.from(stackedItems.querySelectorAll(".notification-block"))
          .filter(block => !block.classList.contains('hidden'));

      const count = visibleItems.length;

      if (count === 0) {
        group.classList.add('hidden');
      } else {
        group.classList.remove('hidden');

        const isHidden = stackedItems.classList.contains("hidden");
        const label = isHidden
            ? `${count} more ${this.humanizeString(type)} notifications`
            : `Hide ${count} ${this.humanizeString(type)} notifications`;

        span.textContent = label;
      }
    });
  }

  checkVisibleNotifications() {
    const visibleNotifications = $(".notification-block:visible");
    if(visibleNotifications.length === 0) {
      $(".notification-sidebar .empty-state").show();
      $(".clear-all-container").hide();
    } else {
      $(".notification-sidebar .empty-state").hide();
      $(".clear-all-container").show();
    }
  }

  closeSidebar() {
    // Use the global sidebar manager to handle closing
    SidebarManager.close('notification-sidebar');
  }

  // Internal function that actually performs the closing
  closeSidebarInternal() {
    if (!this.notificationSidebar) {
      console.warn('Notification sidebar element not found');
      return;
    }
    // Use Tailwind transform classes for smooth animation
    this.notificationSidebar.classList.add('translate-x-full');
  }

  patientAttended() {
    var snd = new Audio("/audio/beep-1.mp3");
    snd.play();
  }

  showToast(data) {
    console.log("Showing toast for notification:", data);
    const toast = document.createElement('div');
    toast.className = 'toast-notification';
    toast.dataset.id = data.id;

    let backgroundColor = null;
    let iconBackgroundColor = null;
    let borderColor = null;
    let iconSvg = '';
    switch (data.data.type) {
      case 'actions':
        if (data.data.style === 'alert' || data.data.style === 'complaint') {
          backgroundColor = '#FEF2F2'; // 50
          borderColor = '#FECACA';
          iconBackgroundColor = '#FEE2E2'; // 100
          iconSvg = `<i style="color: ${borderColor};" class="fa-regular fa-triangle-exclamation text-xl"></i>`;
        } else {
          backgroundColor = '#FFF7ED'; // 50
          borderColor = '#FDBA74';
          iconBackgroundColor = '#FFEDD5'; // 100
          iconSvg = `<i style="color: ${borderColor};" class="fa-regular fa-list text-xl"></i>`;
        }
        break;
      case 'email':
        backgroundColor = '#FFF7ED'; // 50
        borderColor = '#FDBA74';
        iconBackgroundColor = '#FFEDD5'; // 100
        iconSvg = `<i style="color: ${borderColor};" class="fa-thin fa-envelope"></i>`;
        break;
      case 'whatsapp':
        backgroundColor = '#F0FDF4'; // 50
        borderColor = '#86EFAC';
        iconBackgroundColor = '#F0FDF4'; // 100
        iconSvg = `<i style="color: ${borderColor};" class="fa-thin fa-message text-xxl"></i>`;
        break;
      case 'sms':
        backgroundColor = '#EFF6FF'; // 50
        borderColor = '#4D7CFE';
        iconBackgroundColor = '#DBEAFE'; // 100
        iconSvg = `<i style="color: ${borderColor};" class="fa-thin fa-message text-xxl"></i>`;
        break;
      case 'appointment_update':
        backgroundColor = '#FAF5FF'; // 50
        borderColor = '#E9D5FF';
        iconBackgroundColor = '#F3E8FF'; // 100
        iconSvg = `<i style="color: ${borderColor};" class="fa-light fa-calendar-days text-xl font-medium"></i>`;
        break;
      case 'appointment_status':
        if (data.data.style === 'attended') {
          backgroundColor = '#F0FDF4'; // 50
          borderColor = '#BBF7D0';
          iconBackgroundColor = '#F0FDF4'; // 100
        } else if (data.data.style === 'started') {
          backgroundColor = '#FFF7ED'; // 50
          borderColor = '#FDBA74';
          iconBackgroundColor = '#FFEDD5'; // 100
        } else if (data.data.style === 'late') {
          backgroundColor = '#FEF2F2'; // 50
          borderColor = '#FECACA';
          iconBackgroundColor = '#FEE2E2'; // 100
        }
        iconSvg = `<i style="color: ${borderColor};" class="fa-light fa-calendar-days text-xl font-medium"></i>`;
        break;
      case 'medical_history':
        backgroundColor = '#FFF7ED'; // 50
        borderColor = data.data.color; // 200
        iconBackgroundColor = '#FFEDD5'; // 100
        iconSvg = `<i style="color: ${data.data.color};" class="fa-thin fa-triangle-exclamation text-xl"></i>`;
        break;
      case 'patient_consents':
        backgroundColor = '#F5F3FF'; // 50
        borderColor = data.data.color; // 200
        iconBackgroundColor = '#EDE9FE'; // 100
        iconSvg = `<i style="color: ${data.data.color};" class="fa-thin fa-signature text-xl"></i>`;
        break;
      case 'clinical_notes':
        backgroundColor = '#FFF7ED'; // 50
        borderColor = data.data.color; // 200
        iconBackgroundColor = '#FFEDD5'; // 100
        iconSvg = `<i style="color: ${data.data.color};" class="fa-thin fa-note-sticky text-xl"></i>`;
        break;
      case 'patient_finances':
        backgroundColor = '#EFF6FF'; // 50
        borderColor = data.data.color; // 200
        iconBackgroundColor = '#DBEAFE'; // 100
        iconSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="color: ${data.data.color};" class="lucide lucide-pound-sterling h-5 w-5"><path d="M18 7c0-5.333-8-5.333-8 0"></path><path d="M10 7v14"></path><path d="M6 21h12"></path><path d="M6 13h10"></path></svg>`;
        break;
      case 'treatment_plans':
        backgroundColor = '#F0FDF4';
        borderColor = data.data.color;
        iconBackgroundColor = '#F2FBF3';
        iconSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="color: ${data.data.color};" class="lucide lucide-clipboard-list h-5 w-5"><rect width="8" height="4" x="8" y="2" rx="1" ry="1"></rect><path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path><path d="M12 11h4"></path><path d="M12 16h4"></path><path d="M8 11h.01"></path><path d="M8 16h.01"></path></svg>`;
        break;
      case 'labwork':
        backgroundColor = '#fcf4eb';
        borderColor = '#F97316';
        iconBackgroundColor = '#FFEDD5';
        iconSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="color: ${data.data.color};" class="lucide lucide-flask-conical h-5 w-5"><path d="M10 2v7.527a2 2 0 0 1-.211.896L4.72 20.55a1 1 0 0 0 .9 1.45h12.76a1 1 0 0 0 .9-1.45l-5.069-10.127A2 2 0 0 1 14 9.527V2"></path><path d="M8.5 2h7"></path><path d="M7 16h10"></path></svg>`;
      break;
      default:
        iconSvg = '<i class="fa-thin fa-bell"></i>';
        break;
    }

    let badgeHtml = '';
    if (data.badge) {
      badgeHtml = `<span class="notification-badge badge-${data.badge}">${data.badge}</span>`;
    }

    toast.style.border = `0.5px solid ${borderColor}`;
    toast.innerHTML = `
      <div style="background-color: ${backgroundColor};" class="notification-content-area">
        <button class="toast-close-btn" aria-label="Close notification" style="
          position: absolute;
          top: 6px;
          right: 6px;
          background: transparent;
          border: none;
          font-size: 16px;
          font-weight: bold;
          color: ${borderColor};
          cursor: pointer;
          z-index: 10;
          padding: 2px;
          line-height: 1;
        ">&times;</button>
        <div class="notification-icon" style="border-radius: 50%; border: 1px solid ${borderColor}; background-color: ${iconBackgroundColor}">
          ${iconSvg}
        </div>
        <div class="notification-details">
          <div class="notification-title">${data.title}</div>
          <div class="notification-description">${data.description.length > 60 ? data.description.slice(0, 57) + '...' : data.description}</div>
        </div>
      </div>
    `;

    this.toastContainer.appendChild(toast);
    requestAnimationFrame(() => toast.classList.add('show'));

    toast.querySelector('.toast-close-btn')?.addEventListener('click', () => {
      this.removeToast(toast);
    });

    // Auto-remove after 5 seconds
    if(!data.persistent) setTimeout(() => this.removeToast(toast), 7000);
    if(data.shakeEvery) setTimeout(() => this.shakeToast(toast, data.shakeEvery), data.shakeEvery);
    if(data.highlightAfter) setTimeout(() => this.highlightToast(toast, data.highlightAfter), data.highlightAfter);

    if(data.waitingRoom) {
      toast.querySelector(".notification-badge").textContent = "5m";
      toast.classList.add('wr-initial-toast');
      setTimeout(() => {
        // Appointment is due
        toast.classList.remove('wr-initial-toast');
        toast.classList.add('wr-appointment-due');
        var snd = new Audio("/audio/beep-2.mp3");
        // snd.play();

        setTimeout(() => {
          // Appointment is late
          toast.classList.remove('wr-appointment-due');
          toast.classList.add('wr-appointment-late');
        }, 300000)
      }, 300000)
      let timeUntilAppt = 5,
          countdown = setInterval(() => {
            if(!document.body.contains(toast)) return clearInterval(countdown);
            timeUntilAppt--;
            if(timeUntilAppt > 0) {
              toast.querySelector(".notification-badge").textContent = `${timeUntilAppt}m`;
            } else if(timeUntilAppt === 0) {
                toast.querySelector(".notification-badge").textContent = "Now";
            } else if (timeUntilAppt === -5) {
                toast.querySelector(".notification-badge").textContent = "Late";
                clearInterval(countdown);
            }
        }, 7000);
    }
  }

  removeToast(toast) {
    toast.classList.add('hide');
    setTimeout(() => toast.parentNode?.removeChild(toast), 300);
  }

  shakeToast(toast, timeout) {
    if(!document.body.contains(toast)) return;
    const classes = ['animate__animated', 'animate__shakeX', 'animate__repeat-2']
    toast.classList.add(...classes);
    setTimeout(() => toast.classList.remove(...classes), 2000);
    setTimeout(() => this.shakeToast(toast, timeout), timeout);
  }

  highlightToast(toast) {
    toast.classList.add("toast-highlight");
  }

  clearAll() {
    const visibleNotifications = $(".notification-block");
    const visibleNotificationStackedBlocks = $(".notification-stacked-block ");
    fetch('/admin/notifications/clear_all', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({ notification_ids: visibleNotifications.map((index, el) => el.dataset.notificationId).get() })
    }).then(response => {
        if (response.ok) {
            visibleNotifications.remove();
            visibleNotificationStackedBlocks.remove();
            this.updateNotificationCount();
        }
    });
  }

  handleSetReminder() {
    const actionId = document.getElementById('remind-action-id').value;
    const reminderTime = document.getElementById('remind-time').value;

    fetch(`/admin/actions/${actionId}/set_reminder`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({ minutes: reminderTime })
    }).then(response => {
      if (response.ok) {
        // Close the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('remindInModal'));
        modal.hide();

        // Show success toast using the existing toast system
        this.showToast({
          id: Date.now(),
          title: 'Reminder Set',
          description: `You will be reminded in ${document.getElementById('remind-time').options[document.getElementById('remind-time').selectedIndex].text}`,
          icon: 'bell'
        });
      }
    });
  }

  showRemindInDropdownAt(button) {
    // Position the dropdown near the button
    const buttonRect = button.getBoundingClientRect();
    this.remindInDropdown.style.top = `${buttonRect.bottom + window.scrollY}px`;
    this.remindInDropdown.style.left = `${buttonRect.left + window.scrollX}px`;
    this.remindInDropdown.style.width = `${buttonRect.width}px`;

    // Show the dropdown
    this.remindInDropdown.classList.add('show');
  }

  hideRemindInDropdown() {
    this.remindInDropdown.classList.remove('show');
    this.currentNotificationId = null;
    this.currentNotificationIndex = null;
  }
}

// Initialize notification manager when document is ready
document.addEventListener('DOMContentLoaded', () => {
  window.notificationManager = new NotificationManager();
});
