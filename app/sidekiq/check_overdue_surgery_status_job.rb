# frozen_string_literal: true

require 'sidekiq-scheduler'

class CheckOverdueSurgeryStatusJob
  include Sidekiq::<PERSON>

  def perform
    CalendarBooking.past_end_still_in_surgery.find_each do |booking|
      next if !booking.in_surgery? || booking.patient_id.nil?

      message = <<~HTML.strip
        #{booking.patient.full_name}’s appointment was due to finish at #{booking.end_time.strftime('%H:%M')} but is still marked as in surgery.
        If the appointment has been completed, please update the status to completed.
      HTML
      recipients = booking.all_related_users
      recipients.each do |recipient|
        Notification.create(
          recipient: recipient,
          title: "Patient #{booking.patient.full_name} is still in surgery",
          description: message,
          data: { type: 'appointment_update', sender: booking.patient.full_name },
          actions: [
            { text: 'View Calendar', primary: true, action: 'redirect', href: '/admin/calendar_bookings/staff_calendar' },
            { text: 'Mark Read', action: 'mark_as_read' },
            { text: 'Remind Me Later', action: 'remind_in' }
          ]
        )
      end
    end
  end
end
